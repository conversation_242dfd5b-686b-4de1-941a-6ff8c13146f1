import api from './api';
import { FEE_ENDPOINTS } from '../utils/constants';

/**
 * Calculate fees for a specific amount
 * @param {number} amount - Transaction amount
 * @returns {Promise} Promise with fee calculation data
 */
export const calculateFees = async (amount) => {
  const response = await api.get(`${FEE_ENDPOINTS.CALCULATE}?amount=${amount}`);
  return response.data;
};

/**
 * Get current fee structure
 * @returns {Promise} Promise with fee structure data
 */
export const getFeeStructure = async () => {
  const response = await api.get(FEE_ENDPOINTS.STRUCTURE);
  return response.data;
};

/**
 * Get seller earnings preview for specific amount
 * @param {number} amount - Transaction amount
 * @returns {Promise} Promise with seller earnings preview
 */
export const getSellerEarningsPreview = async (amount) => {
  const response = await api.get(`${FEE_ENDPOINTS.SELLER_PREVIEW}?amount=${amount}`);
  return response.data;
};

/**
 * Update Stripe fees (Admin only)
 * @param {number} percentage - Processing fee percentage
 * @param {number} fixedFee - Fixed fee amount
 * @returns {Promise} Promise with updated fee data
 */
export const updateStripeFees = async (percentage, fixedFee) => {
  const response = await api.put(FEE_ENDPOINTS.UPDATE_STRIPE, {
    percentage,
    fixedFee
  });
  return response.data;
};

/**
 * Refresh Stripe fees from API (Admin only)
 * @returns {Promise} Promise with refreshed fee data
 */
export const refreshStripeFees = async () => {
  const response = await api.post(FEE_ENDPOINTS.REFRESH_STRIPE);
  return response.data;
};

export default {
  calculateFees,
  getFeeStructure,
  getSellerEarningsPreview,
  updateStripeFees,
  refreshStripeFees
};
