import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { FaEye, FaDownload, FaFilter, FaSearch, FaMoneyBillWave } from 'react-icons/fa';
import { MdDateRange } from 'react-icons/md';
import { toast } from 'react-toastify';
import { getSellerPayments } from '../../services/paymentService';
import Table from '../common/Table';
import LoadingSkeleton from '../common/LoadingSkeleton';
import { ErrorDisplay } from '../common/ErrorBoundary';
import EarningsBreakdown from './EarningsBreakdown';
import '../../styles/TransactionHistory.css';

const TransactionHistory = () => {
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedTransaction, setSelectedTransaction] = useState(null);
  const [showBreakdownModal, setShowBreakdownModal] = useState(false);
  const [filters, setFilters] = useState({
    status: '',
    dateFrom: '',
    dateTo: '',
    search: ''
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });

  useEffect(() => {
    fetchTransactions();
  }, [pagination.page, filters]);

  const fetchTransactions = async () => {
    try {
      setLoading(true);
      setError(null);

      const queryParams = new URLSearchParams({
        page: pagination.page,
        limit: pagination.limit,
        ...filters
      });

      const response = await getSellerPayments(queryParams.toString());
      
      if (response.success) {
        setTransactions(response.data || []);
        setPagination(prev => ({
          ...prev,
          total: response.pagination?.total || 0,
          totalPages: response.pagination?.totalPages || 0
        }));
      } else {
        throw new Error(response.message || 'Failed to fetch transactions');
      }
    } catch (err) {
      console.error('Error fetching transactions:', err);
      setError(err.message || 'Failed to load transaction history');
      toast.error('Failed to load transaction history');
    } finally {
      setLoading(false);
    }
  };

  const handleViewBreakdown = (transaction) => {
    setSelectedTransaction(transaction);
    setShowBreakdownModal(true);
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount || 0);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status) => {
    const statusClasses = {
      'Completed': 'status-completed',
      'Pending': 'status-pending',
      'Failed': 'status-failed',
      'Refunded': 'status-refunded'
    };

    return (
      <span className={`status-badge ${statusClasses[status] || 'status-default'}`}>
        {status}
      </span>
    );
  };

  const columns = [
    {
      key: 'createdAt',
      label: 'Date',
      render: (transaction) => formatDate(transaction.createdAt)
    },
    {
      key: 'order',
      label: 'Order ID',
      render: (transaction) => (
        <span className="order-id">
          #{transaction.order?._id?.slice(-8).toUpperCase() || 'N/A'}
        </span>
      )
    },
    {
      key: 'buyer',
      label: 'Buyer',
      render: (transaction) => (
        <div className="buyer-info">
          <div className="buyer-name">
            {transaction.buyer?.firstName} {transaction.buyer?.lastName}
          </div>
          <div className="buyer-email">{transaction.buyer?.email}</div>
        </div>
      )
    },
    {
      key: 'amount',
      label: 'Transaction Amount',
      render: (transaction) => (
        <span className="transaction-amount">
          {formatCurrency(transaction.amount)}
        </span>
      )
    },
    {
      key: 'fees',
      label: 'Total Fees',
      render: (transaction) => {
        const totalFees = (transaction.platformFee || 0) + (transaction.stripeProcessingFee || 0);
        return (
          <span className="total-fees">
            -{formatCurrency(totalFees)}
          </span>
        );
      }
    },
    {
      key: 'sellerEarnings',
      label: 'Net Earnings',
      render: (transaction) => (
        <span className="net-earnings">
          {formatCurrency(transaction.sellerEarnings)}
        </span>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (transaction) => getStatusBadge(transaction.status)
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (transaction) => (
        <div className="action-buttons">
          <button
            className="btn-view-breakdown"
            onClick={() => handleViewBreakdown(transaction)}
            title="View earnings breakdown"
          >
            <FaEye />
          </button>
        </div>
      )
    }
  ];

  const calculateTotalEarnings = () => {
    return transactions
      .filter(t => t.status === 'Completed')
      .reduce((total, t) => total + (t.sellerEarnings || 0), 0);
  };

  const calculateTotalFees = () => {
    return transactions
      .filter(t => t.status === 'Completed')
      .reduce((total, t) => total + ((t.platformFee || 0) + (t.stripeProcessingFee || 0)), 0);
  };

  return (
    <div className="transaction-history">
      <div className="history-header">
        <h2>
          <FaMoneyBillWave className="header-icon" />
          Transaction History
        </h2>
        
        <div className="summary-cards">
          <div className="summary-card">
            <div className="card-label">Total Earnings</div>
            <div className="card-value earnings">{formatCurrency(calculateTotalEarnings())}</div>
          </div>
          <div className="summary-card">
            <div className="card-label">Total Fees Paid</div>
            <div className="card-value fees">{formatCurrency(calculateTotalFees())}</div>
          </div>
          <div className="summary-card">
            <div className="card-label">Total Transactions</div>
            <div className="card-value count">{pagination.total}</div>
          </div>
        </div>
      </div>

      <div className="filters-section">
        <div className="filter-group">
          <div className="filter-item">
            <label>Status</label>
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
            >
              <option value="">All Statuses</option>
              <option value="Completed">Completed</option>
              <option value="Pending">Pending</option>
              <option value="Failed">Failed</option>
              <option value="Refunded">Refunded</option>
            </select>
          </div>

          <div className="filter-item">
            <label>From Date</label>
            <input
              type="date"
              value={filters.dateFrom}
              onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
            />
          </div>

          <div className="filter-item">
            <label>To Date</label>
            <input
              type="date"
              value={filters.dateTo}
              onChange={(e) => handleFilterChange('dateTo', e.target.value)}
            />
          </div>

          <div className="filter-item">
            <label>Search</label>
            <div className="search-input">
              <FaSearch className="search-icon" />
              <input
                type="text"
                placeholder="Search by order ID or buyer..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
              />
            </div>
          </div>
        </div>
      </div>

      <div className="table-section">
        {loading ? (
          <LoadingSkeleton type="table" rows={5} />
        ) : error ? (
          <ErrorDisplay
            error={error}
            onRetry={fetchTransactions}
            title="Failed to load transactions"
          />
        ) : (
          <Table
            columns={columns}
            data={transactions}
            emptyMessage="No transactions found"
            className="transactions-table"
          />
        )}

        {pagination.totalPages > 1 && (
          <div className="pagination">
            {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map(page => (
              <button
                key={page}
                className={`page-btn ${page === pagination.page ? 'active' : ''}`}
                onClick={() => handlePageChange(page)}
              >
                {page}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Breakdown Modal */}
      {showBreakdownModal && selectedTransaction && (
        <div className="modal-overlay" onClick={() => setShowBreakdownModal(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>Transaction Breakdown</h3>
              <button
                className="modal-close"
                onClick={() => setShowBreakdownModal(false)}
              >
                ×
              </button>
            </div>
            <div className="modal-body">
              <div className="transaction-details">
                <div className="detail-item">
                  <span>Order ID:</span>
                  <span>#{selectedTransaction.order?._id?.slice(-8).toUpperCase()}</span>
                </div>
                <div className="detail-item">
                  <span>Date:</span>
                  <span>{formatDate(selectedTransaction.createdAt)}</span>
                </div>
                <div className="detail-item">
                  <span>Buyer:</span>
                  <span>{selectedTransaction.buyer?.firstName} {selectedTransaction.buyer?.lastName}</span>
                </div>
              </div>
              
              <EarningsBreakdown
                amount={selectedTransaction.amount}
                showCalculator={false}
                showTitle={false}
                className="modal-breakdown"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TransactionHistory;
