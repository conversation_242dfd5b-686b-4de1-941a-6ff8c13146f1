.earnings-breakdown {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.breakdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.breakdown-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.header-icon {
  color: #3b82f6;
}

.refresh-btn {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s;
  color: #6b7280;
}

.refresh-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.calculator-section {
  margin-bottom: 24px;
}

.calculator-input label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
}

.input-group {
  display: flex;
  align-items: center;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
}

.currency-symbol {
  background: #f9fafb;
  padding: 12px 16px;
  border-right: 1px solid #d1d5db;
  color: #6b7280;
  font-weight: 500;
}

.input-group input {
  flex: 1;
  border: none;
  padding: 12px 16px;
  font-size: 16px;
  outline: none;
}

.calculate-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.calculate-btn:hover:not(:disabled) {
  background: #2563eb;
}

.calculate-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.error-message {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 12px 16px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.loading-message {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px;
  text-align: center;
  color: #6b7280;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.breakdown-content {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.breakdown-summary {
  background: #f8fafc;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-item.total-amount .label {
  color: #6b7280;
  font-weight: 500;
}

.summary-item.total-amount .value {
  color: #374151;
  font-weight: 600;
  font-size: 1.1rem;
}

.summary-item.net-earnings {
  padding-top: 12px;
  border-top: 1px solid #e5e7eb;
}

.summary-item.net-earnings .label {
  color: #059669;
  font-weight: 600;
}

.summary-item.net-earnings .value {
  color: #059669;
  font-weight: 700;
  font-size: 1.25rem;
}

.breakdown-details {
  margin-bottom: 24px;
}

.breakdown-details h4 {
  margin: 0 0 16px 0;
  color: #374151;
  font-size: 1.1rem;
  font-weight: 600;
}

.fee-item {
  padding: 16px 0;
  border-bottom: 1px solid #f3f4f6;
}

.fee-item:last-child {
  border-bottom: none;
}

.fee-total {
  padding: 16px 0;
  border-top: 2px solid #e5e7eb;
  margin-top: 8px;
}

.fee-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.fee-name {
  font-weight: 500;
  color: #374151;
}

.fee-total .fee-name {
  font-weight: 600;
  color: #1f2937;
}

.fee-amount {
  font-weight: 600;
  color: #dc2626;
}

.fee-total .fee-amount {
  color: #dc2626;
  font-size: 1.1rem;
}

.fee-description {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.4;
}

.fee-source {
  font-style: italic;
  color: #9ca3af;
}

.breakdown-visual {
  margin-top: 24px;
}

.visual-bar {
  height: 12px;
  border-radius: 6px;
  overflow: hidden;
  display: flex;
  margin-bottom: 12px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.bar-segment {
  height: 100%;
  cursor: pointer;
  transition: opacity 0.2s;
}

.bar-segment:hover {
  opacity: 0.8;
}

.bar-segment.platform-fee {
  background: #f59e0b;
}

.bar-segment.stripe-fee {
  background: #ef4444;
}

.bar-segment.seller-earnings {
  background: #10b981;
}

.visual-legend {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: #6b7280;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.platform-fee {
  background: #f59e0b;
}

.legend-color.stripe-fee {
  background: #ef4444;
}

.legend-color.seller-earnings {
  background: #10b981;
}

.fee-structure-info {
  background: #f8fafc;
  border-radius: 8px;
  padding: 20px;
}

.fee-structure-info h4 {
  margin: 0 0 16px 0;
  color: #374151;
  font-weight: 600;
}

.structure-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  color: #6b7280;
}

.structure-note {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
  color: #6b7280;
  font-size: 0.875rem;
}

/* Modal specific styles */
.modal-earnings-breakdown {
  margin: 0;
  padding: 0;
  box-shadow: none;
  border: none;
  background: transparent;
}

.modal-earnings-breakdown .breakdown-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
}

/* Responsive design */
@media (max-width: 768px) {
  .earnings-breakdown {
    padding: 16px;
  }

  .breakdown-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .input-group {
    flex-direction: column;
  }

  .currency-symbol {
    border-right: none;
    border-bottom: 1px solid #d1d5db;
  }

  .calculate-btn {
    border-radius: 0 0 8px 8px;
  }

  .visual-legend {
    justify-content: center;
  }
}
