/**
 * Utility functions for calculating platform and Stripe fees
 */

/**
 * Calculate all fees and seller earnings for a transaction
 * @param {number} totalAmount - The total transaction amount
 * @param {number} platformFeePercentage - Platform commission percentage (e.g., 5 for 5%)
 * @returns {object} Object containing all calculated amounts
 */
const calculateTransactionFees = (totalAmount, platformFeePercentage = 5) => {
  // Stripe processing fees (as of 2024)
  const STRIPE_PROCESSING_FEE_PERCENTAGE = 0.029; // 2.9%
  const STRIPE_FIXED_FEE = 0.30; // $0.30

  // Calculate platform fee
  const platformFee = totalAmount * (platformFeePercentage / 100);
  
  // Calculate amount that will be transferred to seller (after platform fee)
  const transferAmount = totalAmount - platformFee;
  
  // Calculate Stripe processing fees that will be deducted from transfer
  const stripeProcessingFee = (transferAmount * STRIPE_PROCESSING_FEE_PERCENTAGE) + STRIPE_FIXED_FEE;
  
  // Calculate actual seller earnings (after both platform fee and Stripe fees)
  const sellerEarnings = transferAmount - stripeProcessingFee;

  return {
    totalAmount: parseFloat(totalAmount.toFixed(2)),
    platformFee: parseFloat(platformFee.toFixed(2)),
    platformFeePercentage,
    transferAmount: parseFloat(transferAmount.toFixed(2)),
    stripeProcessingFee: parseFloat(stripeProcessingFee.toFixed(2)),
    stripeProcessingFeePercentage: STRIPE_PROCESSING_FEE_PERCENTAGE,
    stripeFixedFee: STRIPE_FIXED_FEE,
    sellerEarnings: parseFloat(sellerEarnings.toFixed(2)),
    // Additional calculated fields for transparency
    totalFeesDeducted: parseFloat((platformFee + stripeProcessingFee).toFixed(2)),
    effectivePlatformRate: parseFloat(((platformFee + stripeProcessingFee) / totalAmount * 100).toFixed(2))
  };
};

/**
 * Calculate fees in cents for Stripe API calls
 * @param {number} totalAmount - The total transaction amount in dollars
 * @param {number} platformFeePercentage - Platform commission percentage
 * @returns {object} Object containing amounts in cents
 */
const calculateTransactionFeesInCents = (totalAmount, platformFeePercentage = 5) => {
  const fees = calculateTransactionFees(totalAmount, platformFeePercentage);
  
  return {
    totalAmountCents: Math.round(fees.totalAmount * 100),
    platformFeeCents: Math.round(fees.platformFee * 100),
    transferAmountCents: Math.round(fees.transferAmount * 100),
    stripeProcessingFeeCents: Math.round(fees.stripeProcessingFee * 100),
    sellerEarningsCents: Math.round(fees.sellerEarnings * 100),
    ...fees // Include dollar amounts as well
  };
};

/**
 * Validate that fee calculations are correct
 * @param {object} fees - Fee calculation object
 * @returns {boolean} True if calculations are valid
 */
const validateFeeCalculations = (fees) => {
  const {
    totalAmount,
    platformFee,
    transferAmount,
    stripeProcessingFee,
    sellerEarnings
  } = fees;

  // Check that platform fee + transfer amount equals total amount
  const platformCheck = Math.abs((platformFee + transferAmount) - totalAmount) < 0.01;
  
  // Check that stripe fee + seller earnings equals transfer amount
  const stripeCheck = Math.abs((stripeProcessingFee + sellerEarnings) - transferAmount) < 0.01;
  
  // Check that all amounts are positive
  const positiveCheck = [totalAmount, platformFee, transferAmount, stripeProcessingFee, sellerEarnings]
    .every(amount => amount >= 0);

  return platformCheck && stripeCheck && positiveCheck;
};

module.exports = {
  calculateTransactionFees,
  calculateTransactionFeesInCents,
  validateFeeCalculations
};
