/**
 * Utility functions for calculating platform and Stripe fees
 */

const stripeFeeService = require('../services/stripeFeeService');

/**
 * Calculate all fees and seller earnings for a transaction
 * @param {number} totalAmount - The total transaction amount
 * @param {number} platformFeePercentage - Platform commission percentage (e.g., 5 for 5%)
 * @param {object} stripeFees - Optional Stripe fee override (for performance)
 * @returns {object} Object containing all calculated amounts
 */
const calculateTransactionFees = (totalAmount, platformFeePercentage = 5, stripeFees = null) => {
  // Use provided Stripe fees or default values
  const stripeProcessingFeePercentage = stripeFees ?
    stripeFees.percentageDecimal || (stripeFees.percentage / 100) : 0.029;
  const stripeFixedFee = stripeFees ? stripeFees.fixedFee : 0.30;

  // Calculate platform fee
  const platformFee = totalAmount * (platformFeePercentage / 100);

  // Calculate amount that will be transferred to seller (after platform fee)
  const transferAmount = totalAmount - platformFee;

  // Calculate Stripe processing fees that will be deducted from transfer
  const stripeProcessingFee = (transferAmount * stripeProcessingFeePercentage) + stripeFixedFee;

  // Calculate actual seller earnings (after both platform fee and Stripe fees)
  const sellerEarnings = transferAmount - stripeProcessingFee;

  return {
    totalAmount: parseFloat(totalAmount.toFixed(2)),
    platformFee: parseFloat(platformFee.toFixed(2)),
    platformFeePercentage,
    transferAmount: parseFloat(transferAmount.toFixed(2)),
    stripeProcessingFee: parseFloat(stripeProcessingFee.toFixed(2)),
    stripeProcessingFeePercentage: parseFloat((stripeProcessingFeePercentage * 100).toFixed(2)),
    stripeFixedFee: stripeFixedFee,
    sellerEarnings: parseFloat(sellerEarnings.toFixed(2)),
    // Additional calculated fields for transparency
    totalFeesDeducted: parseFloat((platformFee + stripeProcessingFee).toFixed(2)),
    effectivePlatformRate: parseFloat(((platformFee + stripeProcessingFee) / totalAmount * 100).toFixed(2)),
    // Metadata
    stripeFeeSource: stripeFees ? stripeFees.source : 'default',
    calculatedAt: new Date().toISOString()
  };
};

/**
 * Calculate all fees with dynamic Stripe fee retrieval
 * @param {number} totalAmount - The total transaction amount
 * @param {number} platformFeePercentage - Platform commission percentage
 * @returns {Promise<object>} Object containing all calculated amounts
 */
const calculateTransactionFeesAsync = async (totalAmount, platformFeePercentage = 5) => {
  try {
    const stripeFees = await stripeFeeService.getFeeDetails();
    return calculateTransactionFees(totalAmount, platformFeePercentage, stripeFees);
  } catch (error) {
    console.error('Error getting dynamic Stripe fees, using defaults:', error);
    return calculateTransactionFees(totalAmount, platformFeePercentage);
  }
};

/**
 * Calculate fees in cents for Stripe API calls
 * @param {number} totalAmount - The total transaction amount in dollars
 * @param {number} platformFeePercentage - Platform commission percentage
 * @returns {object} Object containing amounts in cents
 */
const calculateTransactionFeesInCents = (totalAmount, platformFeePercentage = 5) => {
  const fees = calculateTransactionFees(totalAmount, platformFeePercentage);
  
  return {
    totalAmountCents: Math.round(fees.totalAmount * 100),
    platformFeeCents: Math.round(fees.platformFee * 100),
    transferAmountCents: Math.round(fees.transferAmount * 100),
    stripeProcessingFeeCents: Math.round(fees.stripeProcessingFee * 100),
    sellerEarningsCents: Math.round(fees.sellerEarnings * 100),
    ...fees // Include dollar amounts as well
  };
};

/**
 * Validate that fee calculations are correct
 * @param {object} fees - Fee calculation object
 * @returns {boolean} True if calculations are valid
 */
const validateFeeCalculations = (fees) => {
  const {
    totalAmount,
    platformFee,
    transferAmount,
    stripeProcessingFee,
    sellerEarnings
  } = fees;

  // Check that platform fee + transfer amount equals total amount
  const platformCheck = Math.abs((platformFee + transferAmount) - totalAmount) < 0.01;
  
  // Check that stripe fee + seller earnings equals transfer amount
  const stripeCheck = Math.abs((stripeProcessingFee + sellerEarnings) - transferAmount) < 0.01;
  
  // Check that all amounts are positive
  const positiveCheck = [totalAmount, platformFee, transferAmount, stripeProcessingFee, sellerEarnings]
    .every(amount => amount >= 0);

  return platformCheck && stripeCheck && positiveCheck;
};

module.exports = {
  calculateTransactionFees,
  calculateTransactionFeesAsync,
  calculateTransactionFeesInCents,
  validateFeeCalculations,
  stripeFeeService
};
