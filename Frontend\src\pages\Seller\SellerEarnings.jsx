import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { FaMoneyBillWave, FaCalculator, FaHistory, FaChartLine } from 'react-icons/fa';
import { MdTrendingUp, MdAccountBalance } from 'react-icons/md';
import SellerLayout from '../../components/seller/SellerLayout';
import EarningsBreakdown from '../../components/seller/EarningsBreakdown';
import TransactionHistory from '../../components/seller/TransactionHistory';
import { getSellerPayments } from '../../services/paymentService';
import { toast } from 'react-toastify';
import '../../styles/SellerEarnings.css';

const SellerEarnings = () => {
  const { user } = useSelector((state) => state.auth);
  const [activeTab, setActiveTab] = useState('calculator');
  const [earningsStats, setEarningsStats] = useState({
    totalEarnings: 0,
    totalTransactions: 0,
    averageTransaction: 0,
    totalFeesPaid: 0,
    thisMonthEarnings: 0,
    lastMonthEarnings: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchEarningsStats();
  }, []);

  const fetchEarningsStats = async () => {
    try {
      setLoading(true);
      
      // Fetch all completed transactions for stats
      const response = await getSellerPayments('status=Completed&limit=1000');
      
      if (response.success && response.data) {
        const transactions = response.data;
        
        // Calculate stats
        const totalEarnings = transactions.reduce((sum, t) => sum + (t.sellerEarnings || 0), 0);
        const totalFeesPaid = transactions.reduce((sum, t) => 
          sum + ((t.platformFee || 0) + (t.stripeProcessingFee || 0)), 0);
        const totalTransactions = transactions.length;
        const averageTransaction = totalTransactions > 0 ? totalEarnings / totalTransactions : 0;
        
        // Calculate this month and last month earnings
        const now = new Date();
        const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
        
        const thisMonthEarnings = transactions
          .filter(t => new Date(t.createdAt) >= thisMonth)
          .reduce((sum, t) => sum + (t.sellerEarnings || 0), 0);
          
        const lastMonthEarnings = transactions
          .filter(t => {
            const date = new Date(t.createdAt);
            return date >= lastMonth && date <= lastMonthEnd;
          })
          .reduce((sum, t) => sum + (t.sellerEarnings || 0), 0);
        
        setEarningsStats({
          totalEarnings,
          totalTransactions,
          averageTransaction,
          totalFeesPaid,
          thisMonthEarnings,
          lastMonthEarnings
        });
      }
    } catch (error) {
      console.error('Error fetching earnings stats:', error);
      toast.error('Failed to load earnings statistics');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount || 0);
  };

  const calculateGrowthPercentage = () => {
    if (earningsStats.lastMonthEarnings === 0) {
      return earningsStats.thisMonthEarnings > 0 ? 100 : 0;
    }
    return ((earningsStats.thisMonthEarnings - earningsStats.lastMonthEarnings) / earningsStats.lastMonthEarnings) * 100;
  };

  const tabs = [
    { id: 'calculator', label: 'Earnings Calculator', icon: <FaCalculator /> },
    { id: 'history', label: 'Transaction History', icon: <FaHistory /> },
    { id: 'analytics', label: 'Analytics', icon: <FaChartLine /> }
  ];

  return (
    <SellerLayout>
      <div className="seller-earnings">
        <div className="earnings-header">
          <h1>
            <FaMoneyBillWave className="header-icon" />
            Earnings Dashboard
          </h1>
          <p>Track your earnings, view transaction history, and calculate potential profits</p>
        </div>

        {/* Earnings Overview Cards */}
        <div className="earnings-overview">
          <div className="overview-card total-earnings">
            <div className="card-icon">
              <MdAccountBalance />
            </div>
            <div className="card-content">
              <div className="card-label">Total Earnings</div>
              <div className="card-value">{formatCurrency(earningsStats.totalEarnings)}</div>
              <div className="card-subtitle">{earningsStats.totalTransactions} transactions</div>
            </div>
          </div>

          <div className="overview-card monthly-earnings">
            <div className="card-icon">
              <MdTrendingUp />
            </div>
            <div className="card-content">
              <div className="card-label">This Month</div>
              <div className="card-value">{formatCurrency(earningsStats.thisMonthEarnings)}</div>
              <div className={`card-subtitle ${calculateGrowthPercentage() >= 0 ? 'positive' : 'negative'}`}>
                {calculateGrowthPercentage() >= 0 ? '+' : ''}{calculateGrowthPercentage().toFixed(1)}% from last month
              </div>
            </div>
          </div>

          <div className="overview-card average-transaction">
            <div className="card-icon">
              <FaChartLine />
            </div>
            <div className="card-content">
              <div className="card-label">Average Transaction</div>
              <div className="card-value">{formatCurrency(earningsStats.averageTransaction)}</div>
              <div className="card-subtitle">Per completed sale</div>
            </div>
          </div>

          <div className="overview-card fees-paid">
            <div className="card-icon">
              <FaMoneyBillWave />
            </div>
            <div className="card-content">
              <div className="card-label">Total Fees Paid</div>
              <div className="card-value fees">{formatCurrency(earningsStats.totalFeesPaid)}</div>
              <div className="card-subtitle">Platform + Stripe fees</div>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="tab-navigation">
          {tabs.map(tab => (
            <button
              key={tab.id}
              className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
              onClick={() => setActiveTab(tab.id)}
            >
              {tab.icon}
              {tab.label}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className="tab-content">
          {activeTab === 'calculator' && (
            <div className="calculator-tab">
              <div className="tab-header">
                <h2>Earnings Calculator</h2>
                <p>Calculate your potential earnings for any transaction amount</p>
              </div>
              <EarningsBreakdown
                showCalculator={true}
                showTitle={false}
                className="earnings-calculator"
              />
            </div>
          )}

          {activeTab === 'history' && (
            <div className="history-tab">
              <TransactionHistory />
            </div>
          )}

          {activeTab === 'analytics' && (
            <div className="analytics-tab">
              <div className="tab-header">
                <h2>Earnings Analytics</h2>
                <p>Detailed insights into your earnings performance</p>
              </div>
              
              <div className="analytics-grid">
                <div className="analytics-card">
                  <h3>Monthly Comparison</h3>
                  <div className="comparison-data">
                    <div className="comparison-item">
                      <span className="period">This Month</span>
                      <span className="amount">{formatCurrency(earningsStats.thisMonthEarnings)}</span>
                    </div>
                    <div className="comparison-item">
                      <span className="period">Last Month</span>
                      <span className="amount">{formatCurrency(earningsStats.lastMonthEarnings)}</span>
                    </div>
                    <div className="comparison-item growth">
                      <span className="period">Growth</span>
                      <span className={`amount ${calculateGrowthPercentage() >= 0 ? 'positive' : 'negative'}`}>
                        {calculateGrowthPercentage() >= 0 ? '+' : ''}{calculateGrowthPercentage().toFixed(1)}%
                      </span>
                    </div>
                  </div>
                </div>

                <div className="analytics-card">
                  <h3>Fee Breakdown</h3>
                  <div className="fee-analysis">
                    <div className="fee-item">
                      <span>Total Revenue</span>
                      <span>{formatCurrency(earningsStats.totalEarnings + earningsStats.totalFeesPaid)}</span>
                    </div>
                    <div className="fee-item">
                      <span>Total Fees</span>
                      <span className="fees">{formatCurrency(earningsStats.totalFeesPaid)}</span>
                    </div>
                    <div className="fee-item">
                      <span>Net Earnings</span>
                      <span className="earnings">{formatCurrency(earningsStats.totalEarnings)}</span>
                    </div>
                    <div className="fee-item percentage">
                      <span>Effective Fee Rate</span>
                      <span>
                        {earningsStats.totalEarnings + earningsStats.totalFeesPaid > 0 
                          ? ((earningsStats.totalFeesPaid / (earningsStats.totalEarnings + earningsStats.totalFeesPaid)) * 100).toFixed(2)
                          : 0}%
                      </span>
                    </div>
                  </div>
                </div>

                <div className="analytics-card">
                  <h3>Performance Metrics</h3>
                  <div className="metrics-data">
                    <div className="metric-item">
                      <span>Total Transactions</span>
                      <span>{earningsStats.totalTransactions}</span>
                    </div>
                    <div className="metric-item">
                      <span>Average per Transaction</span>
                      <span>{formatCurrency(earningsStats.averageTransaction)}</span>
                    </div>
                    <div className="metric-item">
                      <span>Highest Earning Potential</span>
                      <span>Optimize pricing for better returns</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </SellerLayout>
  );
};

export default SellerEarnings;
