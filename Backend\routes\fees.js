const express = require('express');
const {
  calculateFees,
  getFeeStructure,
  updateStripeFees,
  refreshStripeFees,
  getSellerEarningsPreview
} = require('../controllers/feeController');

const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Public routes
router.get('/calculate', calculateFees);
router.get('/structure', getFeeStructure);

// Protected routes
router.use(protect);

// Seller routes
router.get('/seller-preview', authorize('seller', 'admin'), getSellerEarningsPreview);

// Admin routes
router.put('/stripe', authorize('admin'), updateStripeFees);
router.post('/stripe/refresh', authorize('admin'), refreshStripeFees);

module.exports = router;
