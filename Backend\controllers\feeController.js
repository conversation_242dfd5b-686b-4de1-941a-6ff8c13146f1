const ErrorResponse = require('../utils/errorResponse');
const { calculateTransactionFeesAsync, stripeFeeService } = require('../utils/feeCalculations');
const Setting = require('../models/Setting');

// @desc    Get current fee structure and calculate breakdown for amount
// @route   GET /api/fees/calculate?amount=100
// @access  Public
exports.calculateFees = async (req, res, next) => {
  try {
    const { amount } = req.query;
    
    if (!amount || isNaN(amount) || parseFloat(amount) <= 0) {
      return next(new ErrorResponse('Please provide a valid amount', 400));
    }

    const transactionAmount = parseFloat(amount);
    
    // Get platform commission from settings
    const settings = await Setting.getSingleton();
    const platformFeePercentage = settings.financial?.platformCommissionPercentage || 5;
    
    // Calculate fees with dynamic Stripe rates
    const feeBreakdown = await calculateTransactionFeesAsync(transactionAmount, platformFeePercentage);
    
    // Get Stripe fee details for transparency
    const stripeFeeDetails = await stripeFeeService.getFeeDetails();
    
    res.status(200).json({
      success: true,
      data: {
        ...feeBreakdown,
        stripeFeeDetails: {
          percentage: stripeFeeDetails.percentage,
          fixedFee: stripeFeeDetails.fixedFee,
          description: stripeFeeDetails.description,
          lastUpdate: stripeFeeDetails.lastUpdate,
          source: stripeFeeDetails.source,
          isRecent: stripeFeeDetails.isRecent
        }
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get current fee structure
// @route   GET /api/fees/structure
// @access  Public
exports.getFeeStructure = async (req, res, next) => {
  try {
    // Get platform commission from settings
    const settings = await Setting.getSingleton();
    const platformFeePercentage = settings.financial?.platformCommissionPercentage || 5;
    
    // Get current Stripe fees
    const stripeFeeDetails = await stripeFeeService.getFeeDetails();
    
    res.status(200).json({
      success: true,
      data: {
        platformCommission: {
          percentage: platformFeePercentage,
          description: `${platformFeePercentage}% platform commission`
        },
        stripeProcessing: {
          percentage: stripeFeeDetails.percentage,
          fixedFee: stripeFeeDetails.fixedFee,
          description: stripeFeeDetails.description,
          lastUpdate: stripeFeeDetails.lastUpdate,
          source: stripeFeeDetails.source,
          isRecent: stripeFeeDetails.isRecent
        },
        totalEffectiveRate: {
          description: 'Varies by transaction amount due to fixed Stripe fee',
          examples: [
            await calculateTransactionFeesAsync(10, platformFeePercentage),
            await calculateTransactionFeesAsync(50, platformFeePercentage),
            await calculateTransactionFeesAsync(100, platformFeePercentage),
            await calculateTransactionFeesAsync(500, platformFeePercentage)
          ]
        }
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update Stripe fees (Admin only)
// @route   PUT /api/fees/stripe
// @access  Private/Admin
exports.updateStripeFees = async (req, res, next) => {
  try {
    const { percentage, fixedFee } = req.body;
    
    if (!percentage || !fixedFee || isNaN(percentage) || isNaN(fixedFee)) {
      return next(new ErrorResponse('Please provide valid percentage and fixed fee', 400));
    }

    if (percentage < 0 || percentage > 10) {
      return next(new ErrorResponse('Percentage must be between 0 and 10', 400));
    }

    if (fixedFee < 0 || fixedFee > 5) {
      return next(new ErrorResponse('Fixed fee must be between 0 and 5', 400));
    }

    const updatedFees = await stripeFeeService.manuallyUpdateFees(percentage, fixedFee);
    
    res.status(200).json({
      success: true,
      data: updatedFees,
      message: 'Stripe fees updated successfully'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Refresh Stripe fees from API
// @route   POST /api/fees/stripe/refresh
// @access  Private/Admin
exports.refreshStripeFees = async (req, res, next) => {
  try {
    // Clear cache to force refresh
    stripeFeeService.clearCache();
    
    // Get fresh fees
    const freshFees = await stripeFeeService.getFeeDetails();
    
    res.status(200).json({
      success: true,
      data: freshFees,
      message: 'Stripe fees refreshed successfully'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get seller earnings preview for specific amount
// @route   GET /api/fees/seller-preview?amount=100
// @access  Private/Seller
exports.getSellerEarningsPreview = async (req, res, next) => {
  try {
    const { amount } = req.query;
    
    if (!amount || isNaN(amount) || parseFloat(amount) <= 0) {
      return next(new ErrorResponse('Please provide a valid amount', 400));
    }

    const transactionAmount = parseFloat(amount);
    
    // Get platform commission from settings
    const settings = await Setting.getSingleton();
    const platformFeePercentage = settings.financial?.platformCommissionPercentage || 5;
    
    // Calculate detailed breakdown
    const breakdown = await calculateTransactionFeesAsync(transactionAmount, platformFeePercentage);
    
    // Format for seller display
    const sellerPreview = {
      transactionAmount: breakdown.totalAmount,
      deductions: {
        platformCommission: {
          amount: breakdown.platformFee,
          percentage: breakdown.platformFeePercentage,
          description: `Platform commission (${breakdown.platformFeePercentage}%)`
        },
        stripeProcessing: {
          amount: breakdown.stripeProcessingFee,
          percentage: breakdown.stripeProcessingFeePercentage,
          fixedFee: breakdown.stripeFixedFee,
          description: `Stripe processing (${breakdown.stripeProcessingFeePercentage}% + $${breakdown.stripeFixedFee})`
        },
        total: {
          amount: breakdown.totalFeesDeducted,
          percentage: breakdown.effectivePlatformRate,
          description: `Total fees (${breakdown.effectivePlatformRate}% effective rate)`
        }
      },
      netEarnings: breakdown.sellerEarnings,
      breakdown: breakdown
    };
    
    res.status(200).json({
      success: true,
      data: sellerPreview
    });
  } catch (err) {
    next(err);
  }
};
