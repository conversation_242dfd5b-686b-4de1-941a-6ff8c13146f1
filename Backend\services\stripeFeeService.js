const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const Setting = require('../models/Setting');

/**
 * Service for managing Stripe processing fees
 * Provides dynamic fee retrieval and caching
 */
class StripeFeeService {
  constructor() {
    this.cachedFees = null;
    this.lastUpdate = null;
    this.cacheTimeout = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
  }

  /**
   * Get current Stripe processing fees
   * First checks cache, then database, then Stripe API if needed
   * @returns {Promise<object>} Object containing fee information
   */
  async getProcessingFees() {
    try {
      // Check if we have valid cached fees
      if (this.cachedFees && this.lastUpdate && 
          (Date.now() - this.lastUpdate) < this.cacheTimeout) {
        return this.cachedFees;
      }

      // Get fees from database settings
      const settings = await Setting.getSingleton();
      const dbFees = {
        percentage: settings.financial?.stripeProcessingFeePercentage || 2.9,
        fixedFee: settings.financial?.stripeFixedFee || 0.30,
        lastUpdate: settings.financial?.lastStripeFeesUpdate || new Date(),
        source: 'database'
      };

      // Check if database fees are recent (within 7 days)
      const sevenDaysAgo = new Date(Date.now() - (7 * 24 * 60 * 60 * 1000));
      if (dbFees.lastUpdate > sevenDaysAgo) {
        this.cachedFees = dbFees;
        this.lastUpdate = Date.now();
        return dbFees;
      }

      // Try to get updated fees from Stripe (if possible)
      // Note: Stripe doesn't have a direct API for current fees, so we'll use known rates
      // and provide a mechanism for manual updates
      const updatedFees = await this.fetchLatestStripeFees();
      
      // Update database with new fees
      await this.updateFeesInDatabase(updatedFees);
      
      this.cachedFees = updatedFees;
      this.lastUpdate = Date.now();
      
      return updatedFees;
    } catch (error) {
      console.error('Error getting Stripe processing fees:', error);
      
      // Fallback to default fees
      return {
        percentage: 2.9,
        fixedFee: 0.30,
        lastUpdate: new Date(),
        source: 'fallback',
        error: error.message
      };
    }
  }

  /**
   * Fetch latest Stripe fees
   * Since Stripe doesn't provide a direct API, this method can be extended
   * to check Stripe's pricing page or use other methods
   * @returns {Promise<object>} Updated fee information
   */
  async fetchLatestStripeFees() {
    try {
      // For now, we'll use the current known Stripe rates
      // This can be extended to scrape Stripe's pricing page or use other methods
      
      // Get account information to determine the country and fee structure
      const account = await stripe.accounts.retrieve();
      
      // Default US rates (as of 2024)
      let fees = {
        percentage: 2.9,
        fixedFee: 0.30,
        currency: 'USD',
        country: account.country || 'US',
        lastUpdate: new Date(),
        source: 'stripe_api'
      };

      // Adjust fees based on country if needed
      if (account.country === 'CA') {
        fees.percentage = 2.9;
        fees.fixedFee = 0.30;
        fees.currency = 'CAD';
      } else if (account.country === 'GB') {
        fees.percentage = 2.4;
        fees.fixedFee = 0.20;
        fees.currency = 'GBP';
      }
      // Add more countries as needed

      return fees;
    } catch (error) {
      console.error('Error fetching latest Stripe fees:', error);
      
      // Return current known rates as fallback
      return {
        percentage: 2.9,
        fixedFee: 0.30,
        currency: 'USD',
        country: 'US',
        lastUpdate: new Date(),
        source: 'fallback',
        error: error.message
      };
    }
  }

  /**
   * Update fees in database
   * @param {object} fees - Fee information to store
   */
  async updateFeesInDatabase(fees) {
    try {
      const settings = await Setting.getSingleton();
      
      settings.financial.stripeProcessingFeePercentage = fees.percentage;
      settings.financial.stripeFixedFee = fees.fixedFee;
      settings.financial.lastStripeFeesUpdate = fees.lastUpdate;
      
      await settings.save();
      
      console.log('Updated Stripe fees in database:', {
        percentage: fees.percentage,
        fixedFee: fees.fixedFee,
        lastUpdate: fees.lastUpdate
      });
    } catch (error) {
      console.error('Error updating fees in database:', error);
      throw error;
    }
  }

  /**
   * Manually update Stripe fees (for admin use)
   * @param {number} percentage - Processing fee percentage
   * @param {number} fixedFee - Fixed fee amount
   * @returns {Promise<object>} Updated fee information
   */
  async manuallyUpdateFees(percentage, fixedFee) {
    try {
      const fees = {
        percentage: parseFloat(percentage),
        fixedFee: parseFloat(fixedFee),
        lastUpdate: new Date(),
        source: 'manual_update'
      };

      await this.updateFeesInDatabase(fees);
      
      // Clear cache to force refresh
      this.cachedFees = null;
      this.lastUpdate = null;
      
      return fees;
    } catch (error) {
      console.error('Error manually updating fees:', error);
      throw error;
    }
  }

  /**
   * Get fee information with metadata
   * @returns {Promise<object>} Detailed fee information
   */
  async getFeeDetails() {
    const fees = await this.getProcessingFees();
    
    return {
      ...fees,
      percentageDecimal: fees.percentage / 100,
      description: `${fees.percentage}% + $${fees.fixedFee}`,
      isRecent: fees.lastUpdate > new Date(Date.now() - (7 * 24 * 60 * 60 * 1000)),
      cacheStatus: this.cachedFees ? 'cached' : 'fresh'
    };
  }

  /**
   * Clear cached fees (force refresh on next request)
   */
  clearCache() {
    this.cachedFees = null;
    this.lastUpdate = null;
  }
}

// Export singleton instance
module.exports = new StripeFeeService();
