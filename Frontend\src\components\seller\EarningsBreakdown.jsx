import React, { useState, useEffect } from 'react';
import { FaInfoCircle, FaCalculator, FaMoneyBillWave, FaChartPie } from 'react-icons/fa';
import { MdRefresh } from 'react-icons/md';
import { toast } from 'react-toastify';
import api from '../../services/api';
import '../../styles/EarningsBreakdown.css';

const EarningsBreakdown = ({ 
  amount, 
  showCalculator = true, 
  showTitle = true,
  className = '',
  onEarningsCalculated = null 
}) => {
  const [calculatorAmount, setCalculatorAmount] = useState(amount || '');
  const [breakdown, setBreakdown] = useState(null);
  const [feeStructure, setFeeStructure] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Fetch fee structure on component mount
  useEffect(() => {
    fetchFeeStructure();
  }, []);

  // Calculate breakdown when amount changes
  useEffect(() => {
    if (amount && amount > 0) {
      calculateBreakdown(amount);
    }
  }, [amount]);

  const fetchFeeStructure = async () => {
    try {
      const response = await api.get('/fees/structure');
      setFeeStructure(response.data.data);
    } catch (err) {
      console.error('Error fetching fee structure:', err);
      setError('Failed to load fee structure');
    }
  };

  const calculateBreakdown = async (transactionAmount) => {
    if (!transactionAmount || transactionAmount <= 0) {
      setBreakdown(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await api.get(`/fees/calculate?amount=${transactionAmount}`);
      const data = response.data.data;
      setBreakdown(data);
      
      if (onEarningsCalculated) {
        onEarningsCalculated(data);
      }
    } catch (err) {
      console.error('Error calculating breakdown:', err);
      setError('Failed to calculate earnings breakdown');
      toast.error('Failed to calculate earnings breakdown');
    } finally {
      setLoading(false);
    }
  };

  const handleCalculate = () => {
    const amount = parseFloat(calculatorAmount);
    if (isNaN(amount) || amount <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }
    calculateBreakdown(amount);
  };

  const handleRefresh = () => {
    fetchFeeStructure();
    if (calculatorAmount) {
      calculateBreakdown(parseFloat(calculatorAmount));
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const formatPercentage = (percentage) => {
    return `${percentage.toFixed(2)}%`;
  };

  return (
    <div className={`earnings-breakdown ${className}`}>
      {showTitle && (
        <div className="breakdown-header">
          <h3>
            <FaChartPie className="header-icon" />
            Earnings Breakdown
          </h3>
          <button 
            className="refresh-btn"
            onClick={handleRefresh}
            title="Refresh fee information"
          >
            <MdRefresh />
          </button>
        </div>
      )}

      {showCalculator && (
        <div className="calculator-section">
          <div className="calculator-input">
            <label htmlFor="amount">Transaction Amount</label>
            <div className="input-group">
              <span className="currency-symbol">$</span>
              <input
                id="amount"
                type="number"
                value={calculatorAmount}
                onChange={(e) => setCalculatorAmount(e.target.value)}
                placeholder="Enter amount"
                min="0"
                step="0.01"
              />
              <button 
                className="calculate-btn"
                onClick={handleCalculate}
                disabled={loading}
              >
                <FaCalculator />
                Calculate
              </button>
            </div>
          </div>
        </div>
      )}

      {error && (
        <div className="error-message">
          <FaInfoCircle />
          {error}
        </div>
      )}

      {loading && (
        <div className="loading-message">
          <div className="spinner"></div>
          Calculating earnings...
        </div>
      )}

      {breakdown && (
        <div className="breakdown-content">
          <div className="breakdown-summary">
            <div className="summary-item total-amount">
              <span className="label">Transaction Amount</span>
              <span className="value">{formatCurrency(breakdown.totalAmount)}</span>
            </div>
            <div className="summary-item net-earnings">
              <span className="label">Your Net Earnings</span>
              <span className="value highlight">{formatCurrency(breakdown.sellerEarnings)}</span>
            </div>
          </div>

          <div className="breakdown-details">
            <h4>Fee Breakdown</h4>
            
            <div className="fee-item">
              <div className="fee-header">
                <span className="fee-name">Platform Commission</span>
                <span className="fee-amount">-{formatCurrency(breakdown.platformFee)}</span>
              </div>
              <div className="fee-description">
                {formatPercentage(breakdown.platformFeePercentage)} of transaction amount
              </div>
            </div>

            <div className="fee-item">
              <div className="fee-header">
                <span className="fee-name">Stripe Processing Fee</span>
                <span className="fee-amount">-{formatCurrency(breakdown.stripeProcessingFee)}</span>
              </div>
              <div className="fee-description">
                {formatPercentage(breakdown.stripeProcessingFeePercentage)} + ${breakdown.stripeFixedFee} 
                {breakdown.stripeFeeDetails && (
                  <span className="fee-source">
                    (Updated: {new Date(breakdown.stripeFeeDetails.lastUpdate).toLocaleDateString()})
                  </span>
                )}
              </div>
            </div>

            <div className="fee-total">
              <div className="fee-header">
                <span className="fee-name">Total Fees</span>
                <span className="fee-amount">-{formatCurrency(breakdown.totalFeesDeducted)}</span>
              </div>
              <div className="fee-description">
                Effective rate: {formatPercentage(breakdown.effectivePlatformRate)}
              </div>
            </div>
          </div>

          <div className="breakdown-visual">
            <div className="visual-bar">
              <div 
                className="bar-segment platform-fee"
                style={{ width: `${(breakdown.platformFee / breakdown.totalAmount) * 100}%` }}
                title={`Platform Fee: ${formatCurrency(breakdown.platformFee)}`}
              ></div>
              <div 
                className="bar-segment stripe-fee"
                style={{ width: `${(breakdown.stripeProcessingFee / breakdown.totalAmount) * 100}%` }}
                title={`Stripe Fee: ${formatCurrency(breakdown.stripeProcessingFee)}`}
              ></div>
              <div 
                className="bar-segment seller-earnings"
                style={{ width: `${(breakdown.sellerEarnings / breakdown.totalAmount) * 100}%` }}
                title={`Your Earnings: ${formatCurrency(breakdown.sellerEarnings)}`}
              ></div>
            </div>
            <div className="visual-legend">
              <div className="legend-item">
                <span className="legend-color platform-fee"></span>
                <span>Platform Fee</span>
              </div>
              <div className="legend-item">
                <span className="legend-color stripe-fee"></span>
                <span>Stripe Fee</span>
              </div>
              <div className="legend-item">
                <span className="legend-color seller-earnings"></span>
                <span>Your Earnings</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {feeStructure && !breakdown && (
        <div className="fee-structure-info">
          <h4>Current Fee Structure</h4>
          <div className="structure-item">
            <span>Platform Commission:</span>
            <span>{formatPercentage(feeStructure.platformCommission.percentage)}</span>
          </div>
          <div className="structure-item">
            <span>Stripe Processing:</span>
            <span>{feeStructure.stripeProcessing.description}</span>
          </div>
          <div className="structure-note">
            <FaInfoCircle />
            Enter an amount above to see your exact earnings breakdown
          </div>
        </div>
      )}
    </div>
  );
};

export default EarningsBreakdown;
