.seller-earnings {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

.earnings-header {
  margin-bottom: 32px;
  text-align: center;
}

.earnings-header h1 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin: 0 0 12px 0;
  color: #1f2937;
  font-size: 2rem;
  font-weight: 700;
}

.header-icon {
  color: #059669;
}

.earnings-header p {
  color: #6b7280;
  font-size: 1.1rem;
  margin: 0;
}

.earnings-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.overview-card {
  background: #fff;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.2s, box-shadow 0.2s;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  flex-shrink: 0;
}

.total-earnings .card-icon {
  background: linear-gradient(135deg, #059669, #10b981);
}

.monthly-earnings .card-icon {
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
}

.average-transaction .card-icon {
  background: linear-gradient(135deg, #8b5cf6, #a78bfa);
}

.fees-paid .card-icon {
  background: linear-gradient(135deg, #f59e0b, #fbbf24);
}

.card-content {
  flex: 1;
}

.card-label {
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 4px;
}

.card-value {
  color: #1f2937;
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 4px;
}

.card-value.fees {
  color: #dc2626;
}

.card-subtitle {
  color: #9ca3af;
  font-size: 0.75rem;
}

.card-subtitle.positive {
  color: #059669;
}

.card-subtitle.negative {
  color: #dc2626;
}

.tab-navigation {
  display: flex;
  background: #fff;
  border-radius: 12px;
  padding: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.tab-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  background: transparent;
  color: #6b7280;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.tab-button:hover {
  background: #f3f4f6;
  color: #374151;
}

.tab-button.active {
  background: #3b82f6;
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.tab-content {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.calculator-tab,
.analytics-tab {
  padding: 32px;
}

.history-tab {
  padding: 0;
}

.tab-header {
  margin-bottom: 32px;
  text-align: center;
}

.tab-header h2 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
}

.tab-header p {
  color: #6b7280;
  margin: 0;
}

.earnings-calculator {
  max-width: 600px;
  margin: 0 auto;
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.analytics-card {
  background: #f8fafc;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e5e7eb;
}

.analytics-card h3 {
  margin: 0 0 20px 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.comparison-data,
.fee-analysis,
.metrics-data {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.comparison-item,
.fee-item,
.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e5e7eb;
}

.comparison-item:last-child,
.fee-item:last-child,
.metric-item:last-child {
  border-bottom: none;
}

.comparison-item.growth,
.fee-item.percentage {
  border-top: 2px solid #e5e7eb;
  margin-top: 8px;
  padding-top: 16px;
  font-weight: 600;
}

.period,
.fee-item span:first-child,
.metric-item span:first-child {
  color: #6b7280;
  font-weight: 500;
}

.amount,
.fee-item span:last-child,
.metric-item span:last-child {
  color: #1f2937;
  font-weight: 600;
}

.amount.positive {
  color: #059669;
}

.amount.negative {
  color: #dc2626;
}

.amount.fees,
.fee-item span:last-child.fees {
  color: #dc2626;
}

.amount.earnings,
.fee-item span:last-child.earnings {
  color: #059669;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .earnings-overview {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }

  .overview-card {
    padding: 20px;
  }

  .card-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }

  .card-value {
    font-size: 1.5rem;
  }
}

@media (max-width: 768px) {
  .seller-earnings {
    padding: 16px;
  }

  .earnings-header h1 {
    font-size: 1.75rem;
    flex-direction: column;
    gap: 8px;
  }

  .earnings-overview {
    grid-template-columns: 1fr;
  }

  .tab-navigation {
    flex-direction: column;
    gap: 4px;
  }

  .tab-button {
    justify-content: flex-start;
  }

  .calculator-tab,
  .analytics-tab {
    padding: 20px;
  }

  .analytics-grid {
    grid-template-columns: 1fr;
  }

  .overview-card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .card-content {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .comparison-item,
  .fee-item,
  .metric-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .tab-header {
    margin-bottom: 20px;
  }

  .tab-header h2 {
    font-size: 1.25rem;
  }
}
