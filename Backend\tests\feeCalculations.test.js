const { 
  calculateTransactionFees, 
  calculateTransactionFeesInCents, 
  validateFeeCalculations 
} = require('../utils/feeCalculations');

/**
 * Test fee calculations with various scenarios
 */
function testFeeCalculations() {
  console.log('🧮 Testing Fee Calculations...\n');

  // Test Case 1: $100 sale with 5% platform fee
  console.log('📊 Test Case 1: $100 sale with 5% platform fee');
  const test1 = calculateTransactionFees(100, 5);
  console.log('Total Amount:', test1.totalAmount);
  console.log('Platform Fee (5%):', test1.platformFee);
  console.log('Transfer Amount:', test1.transferAmount);
  console.log('Stripe Processing Fee:', test1.stripeProcessingFee);
  console.log('Seller Earnings:', test1.sellerEarnings);
  console.log('Total Fees Deducted:', test1.totalFeesDeducted);
  console.log('Effective Platform Rate:', test1.effectivePlatformRate + '%');
  console.log('Validation:', validateFeeCalculations(test1) ? '✅ PASS' : '❌ FAIL');
  console.log('');

  // Test Case 2: $50 sale with 5% platform fee
  console.log('📊 Test Case 2: $50 sale with 5% platform fee');
  const test2 = calculateTransactionFees(50, 5);
  console.log('Total Amount:', test2.totalAmount);
  console.log('Platform Fee (5%):', test2.platformFee);
  console.log('Transfer Amount:', test2.transferAmount);
  console.log('Stripe Processing Fee:', test2.stripeProcessingFee);
  console.log('Seller Earnings:', test2.sellerEarnings);
  console.log('Total Fees Deducted:', test2.totalFeesDeducted);
  console.log('Effective Platform Rate:', test2.effectivePlatformRate + '%');
  console.log('Validation:', validateFeeCalculations(test2) ? '✅ PASS' : '❌ FAIL');
  console.log('');

  // Test Case 3: $10 sale with 5% platform fee (small amount)
  console.log('📊 Test Case 3: $10 sale with 5% platform fee (small amount)');
  const test3 = calculateTransactionFees(10, 5);
  console.log('Total Amount:', test3.totalAmount);
  console.log('Platform Fee (5%):', test3.platformFee);
  console.log('Transfer Amount:', test3.transferAmount);
  console.log('Stripe Processing Fee:', test3.stripeProcessingFee);
  console.log('Seller Earnings:', test3.sellerEarnings);
  console.log('Total Fees Deducted:', test3.totalFeesDeducted);
  console.log('Effective Platform Rate:', test3.effectivePlatformRate + '%');
  console.log('Validation:', validateFeeCalculations(test3) ? '✅ PASS' : '❌ FAIL');
  console.log('');

  // Test Case 4: $500 sale with 5% platform fee (large amount)
  console.log('📊 Test Case 4: $500 sale with 5% platform fee (large amount)');
  const test4 = calculateTransactionFees(500, 5);
  console.log('Total Amount:', test4.totalAmount);
  console.log('Platform Fee (5%):', test4.platformFee);
  console.log('Transfer Amount:', test4.transferAmount);
  console.log('Stripe Processing Fee:', test4.stripeProcessingFee);
  console.log('Seller Earnings:', test4.sellerEarnings);
  console.log('Total Fees Deducted:', test4.totalFeesDeducted);
  console.log('Effective Platform Rate:', test4.effectivePlatformRate + '%');
  console.log('Validation:', validateFeeCalculations(test4) ? '✅ PASS' : '❌ FAIL');
  console.log('');

  // Test Case 5: Cents calculation
  console.log('📊 Test Case 5: Cents calculation for $100 sale');
  const test5 = calculateTransactionFeesInCents(100, 5);
  console.log('Total Amount (cents):', test5.totalAmountCents);
  console.log('Platform Fee (cents):', test5.platformFeeCents);
  console.log('Transfer Amount (cents):', test5.transferAmountCents);
  console.log('Stripe Processing Fee (cents):', test5.stripeProcessingFeeCents);
  console.log('Seller Earnings (cents):', test5.sellerEarningsCents);
  console.log('Validation:', validateFeeCalculations(test5) ? '✅ PASS' : '❌ FAIL');
  console.log('');

  console.log('🎯 Summary:');
  console.log('- Platform commission is correctly deducted from total amount');
  console.log('- Stripe processing fees (2.9% + $0.30) are deducted from transfer amount');
  console.log('- Seller receives: Total - Platform Fee - Stripe Fees');
  console.log('- All calculations are validated for mathematical accuracy');
}

// Run tests if this file is executed directly
if (require.main === module) {
  testFeeCalculations();
}

module.exports = { testFeeCalculations };
